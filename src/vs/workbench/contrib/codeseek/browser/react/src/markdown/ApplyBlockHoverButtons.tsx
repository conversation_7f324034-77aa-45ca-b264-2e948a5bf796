import { useState, useEffect, useCallback } from 'react'
import { useAccessor, useURIStreamState, useSettingsState } from '../util/services.js'
import { FeatureNames, isFeatureNameDisabled } from '../../../../common/codeseekSettingsTypes.js'
import { URI } from '../../../../../../../base/common/uri.js'

enum CopyButtonState {
	Idle = 'Copy',
	Copied = 'Copied!',
	Error = 'Could not copy',
}

const COPY_FEEDBACK_TIMEOUT = 2000 // amount of time to say 'Copied!'

const CopyButton = ({ codeStr, className = '' }: { codeStr: string, className?: string }) => {
	const accessor = useAccessor()

	const metricsService = accessor.get('IMetricsService')
	const clipboardService = accessor.get('IClipboardService')
	const [copyButtonState, setCopyButtonState] = useState<CopyButtonState>(CopyButtonState.Idle)

	useEffect(() => {
		if (copyButtonState === CopyButtonState.Idle) return
		setTimeout(() => {
			setCopyButtonState(CopyButtonState.Idle)
		}, COPY_FEEDBACK_TIMEOUT)
	}, [copyButtonState])

	const onCopy = useCallback(() => {
		clipboardService.writeText(codeStr)
			.then(() => { setCopyButtonState(CopyButtonState.Copied) })
			.catch(() => { setCopyButtonState(CopyButtonState.Error) })
		// metricsService.capture('Copy Code', { length: codeStr.length }) // capture the length only
	}, [metricsService, clipboardService, codeStr, setCopyButtonState])

	// 根据状态确定使用哪个图标
	const getIconClass = () => {
		switch (copyButtonState) {
			case CopyButtonState.Copied:
				return 'codicon codicon-check';
			case CopyButtonState.Error:
				return 'codicon codicon-run-errors';
			default:
				return 'codicon codicon-copy';
		}
	};

	return <button
		className={`flex items-center justify-center h-6 scale-75 ${className}`}
		onClick={onCopy}
		title={copyButtonState}
	>
		<span className={getIconClass()}></span>
	</button>
}









export const ApplyBlockHoverButtons = ({
	setApplying,
	codeStr,
	applyBoxId,
	chatId,
	businessEvent,
	uri,
	showCopyOnHover = false  // 默认为false，保持向后兼容
}: {
	setApplying: (applying: boolean) => void,
	codeStr: string,
	applyBoxId: string,
	chatId:string,
	businessEvent:string,
	uri?: URI,
	showCopyOnHover?: boolean
}) => {
	const settingsState = useSettingsState()
	const isDisabled = !!isFeatureNameDisabled(FeatureNames.Apply, settingsState) || !applyBoxId

	const accessor = useAccessor()
	const editCodeService = accessor.get('IEditCodeService')
	const metricsService = accessor.get('IMetricsService')
	const chatThreadService = accessor.get('IChatThreadService')
	const containerId = chatThreadService.getCurrentContainerId();
	const sessionId = chatThreadService.getCurrentThreadId(containerId);
	const threadId = chatThreadService.getCurrentThreadId(containerId);

	const [_, rerender] = useState(0)

	const applyingUri = useCallback(() => editCodeService.applyingURIOfApplyBoxIdRef.current[applyBoxId] ?? null, [applyBoxId])
	const streamState = () => {
		// 优先使用传入的 uri，如果没有则使用 applyingUri
		const targetUri = uri || applyingUri()
		return targetUri ? editCodeService.getURIStreamState({ uri: targetUri, applyBoxId }) : 'idle'
	}

	// listen for stream updates
	useURIStreamState(
		useCallback((uri, newStreamState) => {
			const shouldSkipUpdate = applyingUri()?.fsPath !== uri.fsPath
			if (shouldSkipUpdate) return
			rerender(c => c + 1)
		}, [applyBoxId, editCodeService, applyingUri])
	)

	const onSubmit = useCallback(async () => {
		if (!uri) return
		if (isDisabled) return
		const currentState = editCodeService.getURIStreamState({ uri, applyBoxId })
		// 只有在streaming状态时才阻止新的apply，允许在acceptRejectAll状态下继续apply以支持多次apply
		if (currentState === 'streaming') return


		const newApplyingUri = await editCodeService.startApplying({
			from: 'ClickApply',
			type: 'rewrite',
			applyStr: codeStr,
			uri,
			applyBoxId,
			chatId,
			sessionId,
			businessEvent,
		})



		rerender(c => c + 1)
		// metricsService.capture('Apply Code', { length: codeStr.length, uri }) // capture the length and filePath
	}, [isDisabled, streamState, editCodeService, codeStr, applyBoxId, uri, metricsService, accessor])


	const onInterrupt = useCallback(() => {
		if (streamState() !== 'streaming') return
		const uri = applyingUri()
		if (!uri) return

		editCodeService.interruptURIStreaming({ uri })
		// metricsService.capture('Stop Apply', {})
	}, [streamState, applyingUri, editCodeService, metricsService])


	const isSingleLine = !codeStr.includes('\n')

	const applyButton = <div
		className='flex items-center justify-center h-6 cursor-pointer'
		onClick={onSubmit}
	>
		<span className="codicon codicon-play scale-75"></span>
		<span className="text-xs text-codeseek-fg-1" style={{ margin: '1px 0 0 0' }}>Apply</span>
	</div>

	const stopButton = <div
		className='flex items-center justify-center h-6 cursor-pointer'
		onClick={onInterrupt}
	>
		<span className="codicon codicon-debug-stop scale-75"></span>
		<span className="text-xs text-codeseek-fg-1" style={{ margin: '1px 0 0 0' }}>Stop</span>
	</div>

	const acceptRejectButtons = <>
		<button
			// btn btn-secondary btn-sm border text-sm border-vscode-input-border rounded
			className={`${isSingleLine ? '' : 'px-1 py-0.5'} text-sm text-codeseek-fg-1`}
			onClick={() => {
				const uri = applyingUri()
				if (uri) {
					editCodeService.removeDiffAreas({ uri, behavior: 'accept', removeCtrlKs: false })
					editCodeService.createFilePaths.delete(uri.fsPath) // 清除已接受的文件路径缓存
				}
			}}
		>
			Accept
		</button>
		<button
			// btn btn-secondary btn-sm border text-sm border-vscode-input-border rounded
			className={`${isSingleLine ? '' : 'px-1 py-0.5'} text-sm text-codeseek-fg-1`}
			onClick={() => {
				const uri = applyingUri()
				if (uri) {
					editCodeService.removeDiffAreas({ uri, behavior: 'reject', removeCtrlKs: false })
					editCodeService.shouldDeleteFileOnReject(uri);
				}
			}}
		>
			Reject
		</button>
	</>

	const currStreamState = streamState()

	// Use useEffect to update the applying state when streamState changes
	useEffect(() => {
		setApplying(currStreamState === 'streaming')
	}, [currStreamState, setApplying])

	return <>
		{currStreamState !== 'streaming' && (
			showCopyOnHover
				? <CopyButton codeStr={codeStr} className="opacity-0 group-hover:opacity-100 transition-opacity" />
				: <CopyButton codeStr={codeStr} />
		)}
		{currStreamState === 'idle' && uri && !isDisabled && applyButton}
		{currStreamState === 'streaming' && stopButton}
		{currStreamState === 'acceptRejectAll' && acceptRejectButtons}
	</>
}
